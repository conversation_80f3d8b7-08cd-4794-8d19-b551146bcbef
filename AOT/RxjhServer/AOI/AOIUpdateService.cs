using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HeroYulgang.Helpers;


namespace RxjhServer.AOI
{
    /// <summary>
    /// Service responsible for updating Area of Interest (AOI) for players
    /// Handles efficient visibility updates using the grid-based AOI system
    /// </summary>
    public class AOIUpdateService
    {
        #region Singleton Pattern
        
        private static AOIUpdateService _instance;
        private static readonly object _lock = new object();
        
        /// <summary>
        /// Get the singleton instance of AOIUpdateService
        /// </summary>
        public static AOIUpdateService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new AOIUpdateService();
                    }
                }
                return _instance;
            }
        }
        
        #endregion
        
        #region Private Fields
        
        private readonly AOIManager _aoiManager;
        
        #endregion
        
        #region Constructor
        
        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private AOIUpdateService()
        {
            _aoiManager = AOIManager.Instance;
            LogHelper.WriteLine(LogLevel.Info, "AOIUpdateService initialized successfully");
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Check if two positions are within AOI range
        /// </summary>
        /// <param name="x1">First position X</param>
        /// <param name="y1">First position Y</param>
        /// <param name="x2">Second position X</param>
        /// <param name="y2">Second position Y</param>
        /// <returns>True if positions are within AOI range</returns>
        private bool IsWithinAOI(float x1, float y1, float x2, float y2)
        {
            float dx = x2 - x1;
            float dy = y2 - y1;
            return (dx * dx + dy * dy) <= (AOIManager.AOI_RADIUS * AOIManager.AOI_RADIUS);
        }
        
        #endregion
        
        #region Player AOI Updates
        
        /// <summary>
        /// Update AOI for a specific player with caching optimization
        /// </summary>
        /// <param name="player">Player to update AOI for</param>
        public void UpdatePlayerAOI(Players player)
        {
            try
            {
                if (player == null || player.Client == null || !player.Client.Running)
                {
                    return;
                }

                // Check cache first for performance optimization
                var cachedData = AOICache.Instance.GetPlayerVisibility(player.SessionID, player.PosX, player.PosY, player.MapID);
                if (cachedData != null)
                {
                   // Use cached data for quick update
                   ApplyCachedVisibility(player, cachedData);
                   return;
                }

                // Get all grids within AOI range
                var aoiGrids = _aoiManager.GetAOIGrids(player.PosX, player.PosY, player.MapID);

                var visiblePlayers = new HashSet<Players>();
                var visibleNPCs = new HashSet<NpcClass>();
                var visibleItems = new HashSet<GroundItem>();

                // Collect entities from AOI grids with distance caching
                foreach (var grid in aoiGrids)
                {
                    // Collect visible players
                    foreach (var otherPlayer in grid.Players)
                    {
                        if (otherPlayer != player &&
                            otherPlayer.Client != null &&
                            otherPlayer.Client.Running)
                        {
                            var distance = GetCachedDistance(player.PosX, player.PosY, otherPlayer.PosX, otherPlayer.PosY);
                            if (distance <= AOIManager.AOI_RADIUS && player.FindPlayers(AOIManager.AOI_RADIUS, otherPlayer))
                            {
                                visiblePlayers.Add(otherPlayer);
                            }
                        }
                    }

                    // Collect visible NPCs
                    foreach (var npc in grid.NPCs)
                    {
                        var distance = GetCachedDistance(player.PosX, player.PosY, npc.Rxjh_X, npc.Rxjh_Y);
                        if (distance <= AOIManager.AOI_RADIUS && player.LookInNpc(AOIManager.AOI_RADIUS, npc))
                        {
                            visibleNPCs.Add(npc);
                        }
                    }

                    // Collect visible ground items
                    foreach (var item in grid.GroundItems)
                    {
                        var distance = GetCachedDistance(player.PosX, player.PosY, item.PosX, item.PosY);
                        if (distance <= AOIManager.AOI_RADIUS && player.FindGroundItems(AOIManager.AOI_RADIUS, item))
                        {
                            visibleItems.Add(item);
                        }
                    }
                }

                // Cache the results for future use
                var visiblePlayerIDs = new HashSet<int>(visiblePlayers.Select(p => p.SessionID));
                var visibleNPCIDs = new HashSet<int>(visibleNPCs.Select(n => n.NPC_SessionID));
                var visibleItemIDs = new HashSet<long>(visibleItems.Select(i => i.id));

                AOICache.Instance.CachePlayerVisibility(player.SessionID, player.PosX, player.PosY, player.MapID,
                    visiblePlayerIDs, visibleNPCIDs, visibleItemIDs);

                // Apply differential updates
                UpdatePlayerVisibility(player, visiblePlayers);
                UpdateNPCVisibility(player, visibleNPCs);
                UpdateItemVisibility(player, visibleItems);

                // CRITICAL: Invalidate cache of other players who should now see this player
                // This fixes the reciprocal visibility issue when a new player enters AOI
                InvalidateOtherPlayersCache(player, visiblePlayers);

                LogHelper.WriteLine(LogLevel.Info, $"AOI updated for player {player.CharacterName}: {visiblePlayers.Count} players, {visibleNPCs.Count} NPCs, {visibleItems.Count} items");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating player AOI: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Update player visibility using differential updates
        /// </summary>
        /// <param name="player">Player to update visibility for</param>
        /// <param name="newVisiblePlayers">New set of visible players</param>
        private void UpdatePlayerVisibility(Players player, HashSet<Players> newVisiblePlayers)
        {
            try
            {
                if (player.PlayerList == null)
                {
                    return;
                }
                
                var currentVisible = new HashSet<Players>(player.PlayerList.Values);
                
                // Players to add (newly visible)
                var toAdd = newVisiblePlayers.Except(currentVisible);
                foreach (var newPlayer in toAdd)
                {
                    if (!player.PlayerList.ContainsKey((newPlayer.MapID,newPlayer.SessionID)))
                    {
                        player.PlayerList.Add((newPlayer.MapID,newPlayer.SessionID), newPlayer);
                        player.UpdateCharacterData(newPlayer);
                        
                        // Reciprocal update
                        if (newPlayer.PlayerList != null && !newPlayer.PlayerList.ContainsKey( (player.MapID,player.SessionID)))
                        {
                            newPlayer.PlayerList.Add((player.MapID,player.SessionID), player);
                            newPlayer.UpdateCharacterData(player);
                        }
                    }
                }
                
                // Players to remove (no longer visible)
                var toRemove = currentVisible.Except(newVisiblePlayers);
                foreach (var oldPlayer in toRemove)
                {
                    if (player.PlayerList.ContainsKey((oldPlayer.MapID,oldPlayer.SessionID)))
                    {
                        player.PlayerList.Remove((oldPlayer.MapID,oldPlayer.SessionID));
                        player.NotifyPlayerExit(player, oldPlayer);
                        
                        // Reciprocal update
                        if (oldPlayer.PlayerList != null && oldPlayer.PlayerList.ContainsKey((player.MapID,player.SessionID)))
                        {
                            oldPlayer.PlayerList.Remove((player.MapID,player.SessionID));
                            oldPlayer.NotifyPlayerExit(oldPlayer, player);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating player visibility: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Update NPC visibility using differential updates
        /// </summary>
        /// <param name="player">Player to update NPC visibility for</param>
        /// <param name="newVisibleNPCs">New set of visible NPCs</param>
        private void UpdateNPCVisibility(Players player, HashSet<NpcClass> newVisibleNPCs)
        {
              try
            {
                if (player.NpcList == null)
                {
                    return;
                }
                
                var currentVisible = new HashSet<NpcClass>(player.NpcList.Values);
                
                // NPCs to add (newly visible)
                var toAdd = newVisibleNPCs.Except(currentVisible);
                var addDict = new Dictionary<int, NpcClass>();
                foreach (var newNPC in toAdd)
                {
                    if (!player.NpcList.ContainsKey(newNPC.NPC_SessionID))
                    {
                        player.NpcList.TryAdd(newNPC.NPC_SessionID, newNPC);
                        // No need to call PlayList_Add with AOI Grid system
                        addDict.Add(newNPC.NPC_SessionID, newNPC);
                    }
                }
                
                // NPCs to remove (no longer visible)
                var toRemove = currentVisible.Except(newVisibleNPCs);
                var removeDict = new Dictionary<int, NpcClass>();
                foreach (var oldNPC in toRemove)
                {
                    if (player.NpcList.ContainsKey(oldNPC.NPC_SessionID))
                    {
                        player.NpcList.TryRemove(oldNPC.NPC_SessionID, out _);
                        // No need to call PlayList_Remove with AOI Grid system
                        removeDict.Add(oldNPC.NPC_SessionID, oldNPC);
                    }
                }
                
                // Send updates to client
                if (addDict.Count > 0)
                {
                    NpcClass.UpdateNPC_Spawn(addDict, player);
                }
                
                if (removeDict.Count > 0)
                {
                    NpcClass.UpdateNPC_Despawn(removeDict, player);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating NPC visibility: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Update ground item visibility using differential updates
        /// </summary>
        /// <param name="player">Player to update item visibility for</param>
        /// <param name="newVisibleItems">New set of visible ground items</param>
        private void UpdateItemVisibility(Players player, HashSet<GroundItem> newVisibleItems)
        {
            try
            {
                if (player.ListOfGroundItems == null)
                {
                    return;
                }
                
                var currentVisible = new HashSet<GroundItem>(player.ListOfGroundItems.Values);
                
                // Items to add (newly visible)
                var toAdd = newVisibleItems.Except(currentVisible);
                var addDict = new Dictionary<long, GroundItem>();
                foreach (var newItem in toAdd)
                {
                    if (!player.ListOfGroundItems.ContainsKey(newItem.id))
                    {
                        player.ListOfGroundItems.TryAdd(newItem.id, newItem);
                        if (newItem.PlayerList != null && !newItem.PlayerList.ContainsKey(player.SessionID))
                        {
                            newItem.PlayerList.Add(player.SessionID, player);
                        }
                        addDict.Add(newItem.id, newItem);
                    }
                }
                
                // Items to remove (no longer visible)
                var toRemove = currentVisible.Except(newVisibleItems);
                foreach (var oldItem in toRemove)
                {
                    if (player.ListOfGroundItems.ContainsKey(oldItem.id))
                    {
                        player.ListOfGroundItems.TryRemove(oldItem.id, out _);
                        player.RemoveGroundItem(oldItem.id);
                    }
                }
                
                // Send new items to client
                if (addDict.Count > 0)
                {
                    player.SendGroundItem(addDict);
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error updating item visibility: {ex.Message}");
            }
        }
        
        #endregion
        
        #region Batch Updates
        
        /// <summary>
        /// Update AOI for multiple players efficiently
        /// </summary>
        /// <param name="players">List of players to update</param>
        public void BatchUpdateAOI(List<Players> players)
        {
            try
            {
                if (players == null || players.Count == 0)
                {
                    return;
                }
                
                // Group players by grid for efficient processing
                var playersByGrid = players
                    .Where(p => p != null && p.Client != null && p.Client.Running)
                    .GroupBy(p => _aoiManager.GetGridByPosition(p.PosX, p.PosY, p.MapID))
                    .Where(g => g.Key != null);
                
                foreach (var gridGroup in playersByGrid)
                {
                    var grid = gridGroup.Key;
                    
                    // Only update if grid is dirty or has been a while since last update
                    if (grid.IsDirty || DateTime.Now - grid.LastUpdate > TimeSpan.FromSeconds(5))
                    {
                        foreach (var player in gridGroup)
                        {
                            UpdatePlayerAOI(player);
                        }
                        
                        grid.MarkClean();
                    }
                }
                
                LogHelper.WriteLine(LogLevel.Info, $"Batch AOI update completed for {players.Count} players");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in batch AOI update: {ex.Message}");
            }
        }
        
        #endregion

        #region Optimization Helper Methods

        /// <summary>
        /// Invalidate AOI cache when player state changes
        /// Call this when player HP, equipment, death status, etc. changes
        /// </summary>
        /// <param name="player">Player whose state changed</param>
        public static void InvalidatePlayerStateCache(Players player)
        {
            if (player != null)
            {
                AOICache.Instance.InvalidateCacheForPlayerStateChange(player.SessionID);
            }
        }

        /// <summary>
        /// Invalidate cache of other players who should now see this player
        /// This fixes reciprocal visibility issues when a new player enters AOI
        /// </summary>
        private void InvalidateOtherPlayersCache(Players currentPlayer, HashSet<Players> visiblePlayers)
        {
            try
            {
                foreach (var otherPlayer in visiblePlayers)
                {
                    if (otherPlayer != currentPlayer && otherPlayer.Client != null && otherPlayer.Client.Running)
                    {
                        // Invalidate the other player's cache so they will recalculate AOI
                        // and see the current player
                        AOICache.Instance.InvalidateCacheForPlayer(otherPlayer.SessionID);
                    }
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error invalidating other players cache: {ex.Message}");
            }
        }

        /// <summary>
        /// Apply cached visibility data to a player
        /// </summary>
        private void ApplyCachedVisibility(Players player, AOICache.CachedVisibilityData cachedData)
        {
            try
            {
                // Convert cached IDs back to objects
                var visiblePlayers = new HashSet<Players>();
                var visibleNPCs = new HashSet<NpcClass>();
                var visibleItems = new HashSet<GroundItem>();

                // Get visible players from cache
                foreach (var playerID in cachedData.VisiblePlayerIDs)
                {
                    if (World.allConnectedChars.TryGetValue(playerID, out var cachedPlayer) &&
                        cachedPlayer.Client != null && cachedPlayer.Client.Running)
                    {
                        visiblePlayers.Add(cachedPlayer);
                    }
                }

                // Get visible NPCs from cache
                foreach (var npcID in cachedData.VisibleNPCIDs)
                {
                    var mapNPCs = MapClass.GetnpcTemplate(player.MapID);
                    if (mapNPCs.TryGetValue(npcID, out var cachedNPC))
                    {
                        visibleNPCs.Add(cachedNPC);
                    }
                }

                // Get visible items from cache
                foreach (var itemID in cachedData.VisibleItemIDs)
                {
                    if (World.GroundItemList.TryGetValue(itemID, out var cachedItem))
                    {
                        visibleItems.Add(cachedItem);
                    }
                }

                // Apply updates
                UpdatePlayerVisibility(player, visiblePlayers);
                UpdateNPCVisibility(player, visibleNPCs);
                UpdateItemVisibility(player, visibleItems);

                LogHelper.WriteLine(LogLevel.Info, $"Applied cached AOI for player {player.CharacterName}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error applying cached visibility: {ex.Message}");
                // Invalidate cache and fall back to full update
                AOICache.Instance.InvalidatePlayerCache(player.SessionID);
            }
        }

        /// <summary>
        /// Get distance with caching optimization
        /// </summary>
        private float GetCachedDistance(float x1, float y1, float x2, float y2)
        {
            try
            {
                // Check cache first
                var cachedDistance = AOICache.Instance.GetCachedDistance(x1, y1, x2, y2);
                if (cachedDistance.HasValue)
                {
                    return cachedDistance.Value;
                }

                // Calculate distance
                var distance = (float)Math.Sqrt(Math.Pow(x2 - x1, 2) + Math.Pow(y2 - y1, 2));

                // Cache the result
                AOICache.Instance.CacheDistance(x1, y1, x2, y2, distance);

                return distance;
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error calculating cached distance: {ex.Message}");
                // Fallback to direct calculation
                return (float)Math.Sqrt(Math.Pow(x2 - x1, 2) + Math.Pow(y2 - y1, 2));
            }
        }

        /// <summary>
        /// Optimized batch update using thread pool
        /// </summary>
        public void BatchUpdatePlayersOptimized(List<Players> players)
        {
            try
            {
                if (players == null || players.Count == 0)
                {
                    return;
                }

                // Group players by grid for efficient processing
                var playersByGrid = players
                    .Where(p => p != null && p.Client != null && p.Client.Running)
                    .GroupBy(p => _aoiManager.GetGridByPosition(p.PosX, p.PosY, p.MapID))
                    .Where(g => g.Key != null)
                    .ToList();

                // Process each grid group in thread pool
                foreach (var gridGroup in playersByGrid)
                {
                    var grid = gridGroup.Key;
                    var gridPlayers = gridGroup.ToList();

                    // Only update if grid is dirty or hasn't been updated recently
                    if (grid.IsDirty || DateTime.Now - grid.LastUpdate > TimeSpan.FromSeconds(AOIConfiguration.Instance.MaxUpdateInterval))
                    {
                        // Queue batch update in thread pool
                        AOIThreadPool.Instance.QueueBatchUpdate(gridPlayers, () =>
                        {
                            foreach (var player in gridPlayers)
                            {
                                UpdatePlayerAOI(player);
                            }
                            grid.MarkClean();
                        });
                    }
                }

                LogHelper.WriteLine(LogLevel.Info, $"Optimized batch AOI update queued for {players.Count} players across {playersByGrid.Count} grids");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in optimized batch AOI update: {ex.Message}");
            }
        }

        /// <summary>
        /// Update player AOI specifically for movement events (optimized)
        /// </summary>
        public void UpdatePlayerMovementAOI(Players player)
        {
            try
            {
                if (player == null || player.Client == null || !player.Client.Running)
                {
                    return;
                }

                // Quick movement-specific AOI update
                var grid = _aoiManager.GetGridByPosition(player.PosX, player.PosY, player.MapID);
                if (grid == null)
                {
                    return;
                }

                // Only update if grid is dirty or player moved to new grid
                if (grid.IsDirty || DateTime.Now - grid.LastUpdate > TimeSpan.FromSeconds(10))
                {
                    // Use immediate update for movement
                    UpdatePlayerAOI(player);
                    grid.MarkClean();
                    LogHelper.WriteLine(LogLevel.Debug, $"Movement AOI update completed for player {player.CharacterName}");
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in movement AOI update for player {player?.CharacterName}: {ex.Message}");
            }
        }

        /// <summary>
        /// Immediate AOI update without any throttling or delay - used for critical updates like grid transitions
        /// </summary>
        public void ImmediateUpdatePlayerAOI(Players player)
        {
            try
            {
                if (player == null || player.Client == null || !player.Client.Running)
                {
                    return;
                }

                // Force immediate update regardless of throttling or dirty flags
                UpdatePlayerAOI(player);

                // Mark the grid as clean since we just updated it
                var grid = _aoiManager.GetGridByPosition(player.PosX, player.PosY, player.MapID);
                if (grid != null)
                {
                    grid.MarkClean();
                }

                LogHelper.WriteLine(LogLevel.Debug, $"Immediate AOI update completed for player {player.CharacterName}");
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in immediate AOI update: {ex.Message}");
            }
        }

        /// <summary>
        /// Adaptive update frequency based on player density
        /// </summary>
        public void AdaptiveUpdatePlayer(Players player)
        {
            try
            {
                if (player == null || player.Client == null || !player.Client.Running)
                {
                    return;
                }

                // Get player density in current area
                var grid = _aoiManager.GetGridByPosition(player.PosX, player.PosY, player.MapID);
                if (grid == null)
                {
                    return;
                }

                var playerDensity = grid.PlayerCount;
                var updatePriority = CalculateUpdatePriority(playerDensity, player);

                // Queue update with appropriate priority
                if (updatePriority >= 8) // High priority
                {
                    AOIThreadPool.Instance.QueuePlayerUpdate(player, () => UpdatePlayerAOI(player));
                }
                else if (updatePriority >= 5) // Medium priority
                {
                    // Delay slightly for medium priority
                    Task.Delay(50).ContinueWith(_ => AOIThreadPool.Instance.QueuePlayerUpdate(player, () => UpdatePlayerAOI(player)));
                }
                else // Low priority
                {
                    // Delay more for low priority
                    Task.Delay(100).ContinueWith(_ => AOIThreadPool.Instance.QueuePlayerUpdate(player, () => UpdatePlayerAOI(player)));
                }
            }
            catch (Exception ex)
            {
                LogHelper.WriteLine(LogLevel.Error, $"Error in adaptive player update: {ex.Message}");
            }
        }

        /// <summary>
        /// Calculate update priority based on various factors
        /// </summary>
        private int CalculateUpdatePriority(int playerDensity, Players player)
        {
            int priority = 5; // Base priority

            // Increase priority for high-density areas
            if (playerDensity > 50)
                priority += 3;
            else if (playerDensity > 20)
                priority += 2;
            else if (playerDensity > 10)
                priority += 1;

            // Additional priority factors can be added here when player properties are available
            // For now, we use basic density-based prioritization

            // Slight boost for all active players
            priority += 1;

            return Math.Min(10, priority); // Cap at 10
        }

        #endregion
    }
}
