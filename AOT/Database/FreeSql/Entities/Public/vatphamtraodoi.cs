﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class vatphamtraodoi {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('vatphamtraodoi_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string canvatpham { get; set; }

		[JsonProperty]
		public int? vohuan { get; set; }

		[JsonProperty]
		public int? nguyenbao { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string tienbac { get; set; }

		[JsonProperty]
		public int? sinhmenh { get; set; }

		[JsonProperty]
		public int? congkich { get; set; }

		[JsonProperty]
		public int? phongngu { get; set; }

		[JsonProperty]
		public int? netranh { get; set; }

		[JsonProperty]
		public int? trungdich { get; set; }

		[JsonProperty]
		public int? noicong { get; set; }

		[JsonProperty]
		public int? setitem { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string goivatpham { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string mieuta { get; set; }

	}

}
