﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_mission {

		[JsonProperty]
		public int? fld_id { get; set; }

		[JsonProperty]
		public int? fld_pid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? fld_level { get; set; }

		[JsonProperty]
		public int? fld_zx { get; set; }

		[JsonProperty]
		public int? fld_npcid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_npcname { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_need_item { get; set; }

		[JsonProperty]
		public int? fld_stages { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_msg { get; set; }

		[JsonProperty]
		public int? fld_on { get; set; }

		[JsonProperty]
		public int? fld_map { get; set; }

		[JsonProperty]
		public int? fld_x { get; set; }

		[JsonProperty]
		public int? fld_y { get; set; }

		[JsonProperty]
		public int? fld_type { get; set; }

		[JsonProperty]
		public int? fld_job { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_get_item { get; set; }

		[JsonProperty]
		public byte[] fld_data { get; set; }

	}

}
