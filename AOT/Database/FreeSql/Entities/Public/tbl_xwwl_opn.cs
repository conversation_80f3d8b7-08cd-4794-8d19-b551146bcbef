﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Public {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_opn {

		[JsonProperty]
		public int? fld_index { get; set; }

		[JsonProperty]
		public int? fld_pid { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_name { get; set; }

		[JsonProperty]
		public int? fld_pidx { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_namex { get; set; }

		[JsonProperty]
		public int? fld_number { get; set; }

		[JsonProperty]
		public int? fld_magic1 { get; set; }

		[JsonProperty]
		public int? fld_magic2 { get; set; }

		[JsonProperty]
		public int? fld_magic3 { get; set; }

		[JsonProperty]
		public int? fld_magic4 { get; set; }

		[JsonProperty]
		public int? fld_magic5 { get; set; }

		[JsonProperty]
		public int? fld_fj_觉醒 { get; set; }

		[JsonProperty]
		public int? fld_fj_进化 { get; set; }

		[JsonProperty]
		public int? fld_fj_中级附魂 { get; set; }

		[JsonProperty]
		public int? fld_bd { get; set; }

		[JsonProperty]
		public int? fld_days { get; set; }

		[JsonProperty]
		public int? fld_pp { get; set; }

	}

}
