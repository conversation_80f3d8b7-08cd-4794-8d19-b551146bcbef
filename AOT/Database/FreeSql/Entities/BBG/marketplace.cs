﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.BBG {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class marketplace {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('marketplace_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string product_code { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string item_name { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string seller_id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string seller_name { get; set; }

		[JsonProperty]
		public int? base_amount { get; set; }

		[JsonProperty]
		public long? base_price { get; set; }

		[JsonProperty]
		public long? fld_price { get; set; }

		[JsonProperty]
		public int? current_amount { get; set; }

		[JsonProperty]
		public int? filter_1 { get; set; }

		[JsonProperty]
		public int? filter_2 { get; set; }

		[JsonProperty]
		public int? filter_3 { get; set; }

		[JsonProperty]
		public int? filter_4 { get; set; }

		[JsonProperty]
		public int? filter_5 { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string status { get; set; }

		[JsonProperty]
		public byte[] item { get; set; }

		[JsonProperty]
		public DateTime? created_at { get; set; }

		[JsonProperty]
		public DateTime? expired_at { get; set; }

	}

}
