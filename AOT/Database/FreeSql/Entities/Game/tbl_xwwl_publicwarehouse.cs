﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_publicwarehouse {

		[JsonProperty, Column(IsIdentity = true, InsertValueSql = "nextval('tbl_xwwl_publicwarehouse_id_seq'::regclass)")]
		public int id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string fld_money { get; set; }

		[JsonProperty]
		public byte[] fld_item { get; set; }

		[JsonProperty]
		public byte[] fld_itime { get; set; }

		[JsonProperty]
		public int? fld_zbver { get; set; }

	}

}
