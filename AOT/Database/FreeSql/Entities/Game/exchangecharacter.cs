﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class exchangecharacter {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true, InsertValueSql = "nextval('exchangecharacter_id_seq'::regclass)")]
		public long id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string buyer_id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string seller_id { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string character { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string status { get; set; }

		[JsonProperty]
		public DateTime? created_at { get; set; }

		[JsonProperty]
		public DateTime? updated_at { get; set; }

	}

}
