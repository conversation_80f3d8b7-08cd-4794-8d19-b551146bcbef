﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class tbl_xwwl_pvp {

		[JsonProperty, Column(StringLength = -2)]
		public string santapten { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string a_nguoichoi { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string b_nguoichoi { get; set; }

		[JsonProperty]
		public int? agietnguoisoluong { get; set; }

		[JsonProperty]
		public int? bgietnguoisoluong { get; set; }

		[JsonProperty]
		public int? a_chaytronsolan { get; set; }

		[JsonProperty]
		public int? b_chaytronsolan { get; set; }

		[JsonProperty]
		public int? athuduocnguyenbao { get; set; }

		[JsonProperty]
		public int? bthuduocnguyenbao { get; set; }

		[JsonProperty, Column(StringLength = -2)]
		public string tranhtaiketqua { get; set; }

	}

}
