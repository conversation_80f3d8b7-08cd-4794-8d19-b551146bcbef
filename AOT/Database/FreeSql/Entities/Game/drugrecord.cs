﻿using FreeSql.DatabaseModel;using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading.Tasks;
using Newtonsoft.Json;
using FreeSql.DataAnnotations;
using System.Net;
using Newtonsoft.Json.Linq;
using System.Net.NetworkInformation;
using NpgsqlTypes;
using Npgsql.LegacyPostgis;

namespace HeroYulgang.Database.FreeSql.Entities.Game {

	[JsonObject(MemberSerialization.OptIn), Table(DisableSyncStructure = true)]
	public partial class drugrecord {

		[JsonProperty, Column(IsPrimary = true, IsIdentity = true)]
		public int id { get; set; }

		[JsonProperty, Column(DbType = "varchar", IsNullable = false)]
		public string accountid { get; set; }

		[JsonProperty, Column(DbType = "varchar", IsNullable = false)]
		public string charactername { get; set; }

		[JsonProperty]
		public int itemid { get; set; }

		[JsonProperty]
		public int amount { get; set; }

		[JsonProperty, Column(DbType = "date", InsertValueSql = "now()")]
		public DateTime? created_at { get; set; }

	}

}
