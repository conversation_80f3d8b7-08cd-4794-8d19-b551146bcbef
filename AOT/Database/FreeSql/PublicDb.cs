using FreeSql;
using Microsoft.Extensions.Configuration;
using HeroYulgang.Database.FreeSql.Configuration;
using HeroYulgang.Database.FreeSql.Entities.Public;
using RxjhServer;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.IO;
using System;
using System.Linq;
using HeroYulgang.Core;
using HeroYulgang.Services;
using HeroYulgang.Database.FreeSql.Entities.BBG;
using HeroYulgang.Helpers;

namespace HeroYulgang.Database.FreeSql
{
    /// <summary>
    /// Simple PublicDb instance - like Node.js module
    /// Direct access to template data with caching
    /// </summary>
    public static class PublicDb
    {
        private static IFreeSql? _freeSql;
        private static readonly Dictionary<int, tbl_xwwl_item> _itemCache = new();
        private static readonly Dictionary<int, tbl_xwwl_monster> _monsterCache = new();
        private static bool _isInitialized = false;
        private static readonly object _lock = new object();

        /// <summary>
        /// Initialize PublicDb connection
        /// </summary>
        public static async Task<bool> InitializeAsync()
        {
            try
            {
                if (_isInitialized) return true;

                lock (_lock)
                {
                    if (_isInitialized) return true;
                    var connectionString = ConfigManager.Instance.PogresSettings.PublicDb;
                    Logger.Instance.Info("PublicDb connection string: " + connectionString);
                    // Create FreeSql instance
                    _freeSql = new FreeSqlBuilder()
                        .UseConnectionString(DataType.PostgreSQL, connectionString)
                        .UseAutoSyncStructure(false)
                        .UseNoneCommandParameter(true)
                        .Build();

                    Logger.Instance.Info("✓ PublicDb initialized successfully");
                    _isInitialized = true;
                }

                // Load all templates into cache
                await LoadAllTemplatesAsync();
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to initialize PublicDb: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Load all templates into cache
        /// </summary>
        private static async Task LoadAllTemplatesAsync()
        {
            try
            {
                if (_freeSql == null) return;

                // Load items
                var items = await _freeSql.Select<tbl_xwwl_item>().ToListAsync();
                lock (_lock)
                {
                    _itemCache.Clear();
                    foreach (var item in items.Where(i => i.fld_pid.HasValue))
                    {
                        _itemCache[item.fld_pid!.Value] = item;
                    }
                }
                Logger.Instance.Info($"✓ Loaded {_itemCache.Count} item templates");

                // Load monsters
                var monsters = await _freeSql.Select<tbl_xwwl_monster>().ToListAsync();
                lock (_lock)
                {
                    _monsterCache.Clear();
                    foreach (var monster in monsters.Where(m => m.fld_pid.HasValue))
                    {
                        _monsterCache[monster.fld_pid!.Value] = monster;
                    }
                }
                Logger.Instance.Info($"✓ Loaded {_monsterCache.Count} monster templates");
                // Load monster set base
                LoadMonsterSetBase();
                LoadDrop();
                LoadGSDrop();
                LoadDCHDrop();
                LoadBossDrop();
                LoadItemOption();
                LoadTreasureChest();
                LoadSetItem();
                SetMagic();
                LoadNpcShop();
                SetMoveCommand();
                SetGlobalMessage();
                SetLevelUpReward();
                SetItemExchange();
                LoadMobile();
                LoadKill();
                LoadMaxOptionForItem();
                LoadAscentionAbility();
                LoadAbilityEffects();
                LoadStoneOption();
                LoadGiftCode();
                LoadGiftCodeReward();
                LoadUpgradeItem();
                
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to load templates: {ex.Message}");
            }
        }

        public static void LoadUpgradeItem()
        {
            try
            {
                World.List_UpgradeItem.Clear();
                var upgradeItems = _freeSql.Select<tbl_upgrade_item>().ToList();
                foreach (var upgradeItem in upgradeItems)
                {
                    if (upgradeItem.itemid == null) continue;
                    World.List_UpgradeItem.Add((int)upgradeItem.itemid,new UpgradeItemClass
                    {
                        ID = upgradeItem.id ,
                        ItemID = upgradeItem.itemid ?? 0,
                        ItemName = upgradeItem.itemname ?? string.Empty,
                        ItemLevel = upgradeItem.itemlevel ?? 0,
                        ItemType = upgradeItem.itemtype ?? 0,
                        Upgrade_PP = upgradeItem.upgrade_pp ?? 0,
                        NguyenLieu_ID = upgradeItem.nguyenlieu_id ?? 0,
                        GiamCuongHoa = upgradeItem.giamcuonghoa ?? 0,
                        YeuCauCuongHoa = upgradeItem.yeucaucuonghoa ?? 0
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.List_UpgradeItem.Count} upgrade item templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to load upgrade item: {ex.Message}");
            }
        }

        public static void LoadGiftCodeReward()
        {
            try
            {
                World.GiftCodeRewards.Clear();
                var giftCodeRewards = _freeSql.Select<giftcode_rewards>().ToList();
                foreach (var giftCodeReward in giftCodeRewards)
                {
                    World.GiftCodeRewards.Add(new GiftCodeRewardsClass
                    {
                        Type = giftCodeReward.type ?? 0,
                        Rewards = giftCodeReward.rewards ?? string.Empty
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.GiftCodeRewards.Count} gift code reward templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to load gift code reward: {ex.Message}");
            }
        }

        public static void LoadGiftCode()
        {
            try
            {
                World.GiftCode.Clear();
                var giftCodes = _freeSql.Select<giftcode>().ToList();
                foreach (var giftCode in giftCodes)
                {
                    World.GiftCode.Add(new GiftcodeClass
                    {
                        GiftCode = giftCode.code ?? string.Empty,
                        Type = giftCode.type ?? 0
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.GiftCode.Count} gift code templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to load gift code: {ex.Message}");
            }
        }

        /// <summary>
        /// Load all ranking
        /// </summary>

        
        public static void LoadAbilityEffects()
        {
            try
            {
                World.KhiCongTangThem.Clear();
                var abilityEffects = _freeSql.Select<tbl_xwwl_skill>().ToList();
                foreach (var abilityEffect in abilityEffects)
                {
                    World.KhiCongTangThem.Add(abilityEffect.fld_pid ?? 0, new()
                    {
                        FLD_PID = abilityEffect.fld_pid ?? 0,
                        FLD_INDEX = abilityEffect.fld_index ?? 0,
                        FLD_JOB = abilityEffect.fld_job ?? 0,
                        FLD_NAME = abilityEffect.fld_name ?? string.Empty,
                        FLD_BonusRateValuePerPoint1 = abilityEffect.fld_bonusratevalueperpoint1 ?? 0,
                        FLD_BonusRateValuePerPoint2 = abilityEffect.fld_bonusratevalueperpoint2 ?? 0
                    });

                }
                for (var j = 0; j < 12; j++)
                {
                    for (var k = 1; k < 14; k++)
                    {
                        World.KhiCong_CoBan_ID.Add(World.method_0(j, k));
                    }
                }
                World.KhiCong_CoBan_ID.Sort();
                Logger.Instance.Info($"✓ Loaded {World.KhiCongTangThem.Count} ability effect templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error load ability effect " + ex.Message);
            }
        }
        /// <summary>
        /// Load Option của các loại đá 
        /// </summary>

        public static void LoadStoneOption()
        {
            try
            {
                World.clear();
                var stones = _freeSql.Select<tbl_xwwl_stone>().ToList();
                foreach (var stone in stones)
                {
                    World.SetStone(stone.fld_type ?? 0, stone.fld_value ?? 0, stone.fld_tanggiam ?? 0);
                }
                Logger.Instance.Info($"✓ Loaded {World.wg40} stone option templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error load stone option " + ex.Message);
            }
        }

        public static void LoadAscentionAbility()
        {
            try
            {
                World.ThangThienKhiCongList.Clear();
                var ascensionAbilities = _freeSql.Select<thangthienkhicong>().ToList();
                foreach (var ascensionAbility in ascensionAbilities)
                {
                    World.ThangThienKhiCongList.Add(ascensionAbility.khicongid ?? 0, new X_Thang_Thien_Khi_Cong_Tong_Loai
                    {
                        KhiCongID = ascensionAbility.khicongid ?? 0,
                        KhiCongTen = ascensionAbility.khicongten ?? string.Empty,
                        VatPham_ID = ascensionAbility.vatpham_id ?? 0,
                        NhanVatNgheNghiep1 = ascensionAbility.nhanvatnghenghiep1 ?? 0,
                        NhanVatNgheNghiep2 = ascensionAbility.nhanvatnghenghiep2 ?? 0,
                        NhanVatNgheNghiep3 = ascensionAbility.nhanvatnghenghiep3 ?? 0,
                        NhanVatNgheNghiep4 = ascensionAbility.nhanvatnghenghiep4 ?? 0,
                        NhanVatNgheNghiep5 = ascensionAbility.nhanvatnghenghiep5 ?? 0,
                        NhanVatNgheNghiep6 = ascensionAbility.nhanvatnghenghiep6 ?? 0,
                        NhanVatNgheNghiep7 = ascensionAbility.nhanvatnghenghiep7 ?? 0,
                        NhanVatNgheNghiep8 = ascensionAbility.nhanvatnghenghiep8 ?? 0,
                        NhanVatNgheNghiep9 = ascensionAbility.nhanvatnghenghiep9 ?? 0,
                        NhanVatNgheNghiep10 = ascensionAbility.nhanvatnghenghiep10 ?? 0,
                        NhanVatNgheNghiep11 = ascensionAbility.nhanvatnghenghiep11 ?? 0,
                        NhanVatNgheNghiep12 = ascensionAbility.nhanvatnghenghiep12 ?? 0,
                        NhanVatNgheNghiep13 = ascensionAbility.nhanvatnghenghiep13 ?? 0,
                        FLD_BonusRateValuePerPoint = ascensionAbility.fld_bonusratevalueperpoint ?? 0,
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.ThangThienKhiCongList.Count} ascension ability templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error load Ascention Ability " + ex.Message);
            }
        }

        public static void LoadMaxOptionForItem()
        {
            try
            {
                World.KiemTraThietBiList.Clear();
                var maxOptions = _freeSql.Select<kiemtrathietbi>().ToList();
                for (int i = 0; i < maxOptions.Count; i++)
                {
                    World.KiemTraThietBiList.Add((int)maxOptions[i].vatphamloaihinh, new X_Kiem_Tra_Thiet_Bi_Loai
                    {
                        VatPhamCaoNhatCongKichGiaTri = maxOptions[i].vatphamcaonhatcongkichgiatri ?? 0,
                        VatPhamCaoNhatPhongNguGiaTri = maxOptions[i].vatphamcaonhatphongngugiatri ?? 0,
                        VatPhamCaoNhatHPGiaTri = maxOptions[i].vatphamcaonhathpgiatri ?? 0,
                        VatPhamCaoNhatNoiCongGiaTri = maxOptions[i].vatphamcaonhatnoiconggiatri ?? 0,
                        VatPhamCaoNhatTrungDichGiaTri = maxOptions[i].vatphamcaonhattrungdichgiatri ?? 0,
                        VatPhamCaoNhatNeTranhGiaTri = maxOptions[i].vatphamcaonhatnetranhgiatri ?? 0,
                        VatPhamCaoNhatCongKichVoCongGiaTri = maxOptions[i].vatphamcaonhatcongkichvoconggiatri ?? 0,
                        VatPhamCaoNhatKhiCongGiaTri = maxOptions[i].vatphamcaonhatkhiconggiatri ?? 0,
                        VatPhamCaoNhatPhuHonGiaTri = maxOptions[i].vatphamcaonhatphuhongiatri ?? 0,
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.KiemTraThietBiList.Count} max option for item templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error load max option for item " + ex.Message);
            }
        }


        /// <summary>
        /// Load kill Unknow
        /// </summary>
        public static void LoadKill()
        {
            try
            {
                World.Kill.Clear();
                var kills = _freeSql.Select<xwwl_kill>().ToList();
                foreach (var kill in kills)
                {
                    World.Kill.Add(new KillClass
                    {
                        Txt = kill.txt ?? string.Empty,
                        Sffh = kill.sffh ?? 0,
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.Kill.Count} kill templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error load kill " + ex.Message);
            }
        }

        public static void LoadMobile()
        {
            try
            {
                World.DiDong.Clear();
                var mobiles = _freeSql.Select<tbl_xwwl_map>().ToList();
                foreach (var mobile in mobiles)
                {
                    World.DiDong.Add(new X_Toa_Do_Class
                    {
                        Rxjh_name = mobile.fld_name ?? string.Empty,
                        Rxjh_Map = (int)mobile.fld_mid,
                        Rxjh_X = (float)mobile.x,
                        Rxjh_Y = (float)mobile.y,
                        Rxjh_Z = 15f
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.DiDong.Count} mobile templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"Error load mobile " + ex.Message);
            }
        }

        public static void SetItemExchange()
        {
            try
            {
                World.VatPhamTraoDoi.Clear();
                var itemExchanges = _freeSql.Select<vatphamtraodoi>().ToList();
                foreach (var itemExchange in itemExchanges)
                {
                    World.VatPhamTraoDoi.Add((int)itemExchange.id, new()
                    {
                        CanVatPham = itemExchange.canvatpham ?? string.Empty,
                        VoHuan = itemExchange.vohuan ?? 0,
                        NguyenBao = itemExchange.nguyenbao ?? 0,
                        TienBac = itemExchange.tienbac.ToString(),
                        SinhMenh = itemExchange.sinhmenh ?? 0,
                        CongKich = itemExchange.congkich ?? 0,
                        PhongNgu = itemExchange.phongngu ?? 0,
                        NeTranh = itemExchange.netranh ?? 0,
                        TrungDich = itemExchange.trungdich ?? 0,
                        NoiCong = itemExchange.noicong ?? 0,
                        Set = itemExchange.setitem ?? 0,
                        GoiVatPham = itemExchange.goivatpham ?? string.Empty,
                    });
                }
                Console.WriteLine($"✓ Loaded {World.VatPhamTraoDoi.Count} item exchange templates");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SetItemExchange Error " + ex.Message);
            }
        }

        public static void SetLevelUpReward()
        {
            try
            {
                World.DangCapBanThuong.Clear();
                var rewards = _freeSql.Select<dangcapbanthuong>().ToList();
                int i = 0;
                foreach (var reward in rewards)
                {
                    World.DangCapBanThuong.Add(i, new()
                    {
                        DangCap = reward.dangcap ?? 0,
                        VoHuan = reward.vohuan ?? 0,
                        NguyenBao = reward.nguyenbao ?? 0,
                        TienBac = reward.tienbac.ToString(),
                        SinhMenh = reward.sinhmenh ?? 0,
                        CongKich = reward.congkich ?? 0,
                        PhongNgu = reward.phongngu ?? 0,
                        NeTranh = reward.netranh ?? 0,
                        TrungDich = reward.trungdich ?? 0,
                        NoiCong = reward.noicong ?? 0,
                        Set = reward.setitem ?? 0,
                        GoiVatPham = reward.goivatpham ?? string.Empty,
                    });
                    i++;
                }
                Console.WriteLine($"✓ Loaded {World.DangCapBanThuong.Count} level up reward templates");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"SetLevelUpReward Error " + ex.Message);
            }
        }

        public static void SetGlobalMessage()
        {
            try
            {
                World.ThongBao.Clear();
                var globalMessages = _freeSql.Select<tbl_xwwl_gg>().ToList();
                foreach (var globalMessage in globalMessages)
                {
                    World.ThongBao.Add((int)globalMessage.id, new()
                    {
                        msg = globalMessage.txt,
                        type = globalMessage.type ?? 0,
                    });
                }
                Console.WriteLine($"✓ Loaded {World.ThongBao.Count} global messages");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi tải thông báo " + ex.Message);
            }
        }

        public static void SetMoveCommand()
        {
            try
            {
                World.Mover.Clear();
                var moveCommands = _freeSql.Select<tbl_xwwl_vome>().ToList();
                foreach (var moveCommand in moveCommands)
                {
                    World.Mover.Add(new MoveClass
                    {
                        MAP = (int)moveCommand.map,
                        X = (float)moveCommand.x,
                        Y = (float)moveCommand.y,
                        Z = (float)moveCommand.z,
                        ToMAP = (int)moveCommand.tomap,
                        ToX = (float)moveCommand.tox,
                        ToY = (float)moveCommand.toy,
                        ToZ = (float)moveCommand.toz,
                    });
                }
                Console.WriteLine($"✓ Loaded {World.Mover.Count} move command templates");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Lỗi khi tải move command " + ex.Message);
            }
        }

        public static void LoadNpcShop()
        {
            try
            {
                World.Shop.Clear();
                var npcshops = _freeSql.Select<tbl_xwwl_sell>().ToList();
                foreach (var npcshop in npcshops)
                {
                    World.Shop.Add(new ShopClass
                    {
                        FLD_NID = (int)npcshop.fld_nid,
                        FLD_INDEX = npcshop.fld_index ?? 0,
                        FLD_PID = (int)npcshop.fld_pid,
                        FLD_MONEY = npcshop.fld_money ?? 0,
                        FLD_MAGIC0 = npcshop.fld_magic0 ?? 0,
                        FLD_MAGIC1 = npcshop.fld_magic1 ?? 0,
                        FLD_MAGIC2 = npcshop.fld_magic2 ?? 0,
                        FLD_MAGIC3 = npcshop.fld_magic3 ?? 0,
                        FLD_MAGIC4 = npcshop.fld_magic4 ?? 0,
                        CanVoHuan = npcshop.fld_canvohuan ?? 0,
                        FLD_DAYS = npcshop.fld_days ?? 0,
                        FLD_BD = npcshop.fld_bd ?? 0,
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.Shop.Count} npc shop templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to load npc shop: {ex.Message}");
            }
        }

        public static void SetMagic()
        {
            try
            {
                World.MagicList.Clear();
                var magics = _freeSql.Select<tbl_xwwl_kongfu>().ToList();
                foreach (var magic in magics)
                {
                    World.MagicList.Add(magic.fld_pid ?? 0, new X_Vo_Cong_Loai
                    {
                        FLD_NAME = magic.fld_name ?? string.Empty,
                        FLD_AT = magic.fld_at ?? 0,
                        FLD_EFFERT = magic.fld_effert ?? 0,
                        FLD_INDEX = magic.fld_index ?? 0,
                        FLD_JOB = magic.fld_job ?? 0,
                        FLD_JOBLEVEL = magic.fld_joblevel ?? 0,
                        FLD_LEVEL = magic.fld_level ?? 0,
                        FLD_MP = magic.fld_mp ?? 0,
                        FLD_NEEDEXP = magic.fld_needexp ?? 0,
                        FLD_PID = magic.fld_pid ?? 0,
                        FLD_TYPE = magic.fld_type ?? 0,
                        FLD_ZX = magic.fld_zx ?? 0,
                        FLD_CongKichSoLuong = magic.fld_congkichsoluong ?? 0,
                        FLD_VoCongLoaiHinh = magic.fld_vocongloaihinh ?? 0,
                        FLD_TIME = magic.fld_time ?? 0,
                        FLD_DEATHTIME = magic.fld_deathtime ?? 0,
                        FLD_CDTIME = magic.fld_cdtime ?? 0,
                        FLD_VoCongToiCaoDangCap = magic.fld_vocongtoicaodangcap ?? 0,
                        FLD_MoiCapThemMP = magic.fld_moicapthemmp ?? 0,
                        FLD_MoiCapThemLichLuyen = magic.fld_moicapthemlichluyen ?? 0,
                        FLD_MoiCapNguyHai = magic.fld_moicapnguyhai ?? string.Empty,
                        FLD_MoiCapThemNguyHai = magic.fld_moicapthemnguyhai ?? 0,
                        FLD_MoiCapVoCongDiemSo = magic.fld_moicapvocongdiemso ?? 0,
                        FLD_MoiCapThemTuLuyenDangCap = magic.fld_moicapthemtuluyendangcap ?? 0,
                        Time_Animation = magic.time_animation ?? 0,
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.MagicList.Count} magic templates");
            }
            catch (System.Exception Ex)
            {
                Logger.Instance.Error($"Error load magic " + Ex.Message);
            }
        }

        public static void LoadSetItem()
        {
            try
            {
                World.Set_SoLieu.Clear();
                var sets = _freeSql.Select<itmeclss>().ToList();
                foreach (var set in sets)
                {
                    World.Set_SoLieu.Add(new ItemSellClass
                    {
                        ID = set.id ?? 0,
                        Type = set.fld_type ?? 0,
                        Reside = set.fld_reside ?? 0,
                        name = set.fld_name ?? string.Empty,
                        sql = set.fld_sql ?? string.Empty,
                        Magic0 = set.fld_magic0 ?? 0,
                        Magic1 = set.fld_magic1 ?? 0,
                        Magic2 = set.fld_magic2 ?? 0,
                        Magic3 = set.fld_magic3 ?? 0,
                        Magic4 = set.fld_magic4 ?? 0,
                        Magic5 = set.fld_magic5 ?? 0,
                        NJ = set.fld_fj_nj ?? 0,
                        DAYS = set.fld_days ?? 0,
                        BD = set.fld_bd ?? 0,
                        ThucTinh = set.fld_fj_thuctinh ?? 0,
                        TienHoa = set.fld_fj_tienhoa ?? 0,
                        TrungCapPhuHon = set.fld_fj_trungcapphuhon ?? 0,
                    });
                }
                Console.WriteLine($"✓ Loaded {World.Set_SoLieu.Count} set item templates");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Failed to load set item: {ex.Message}");
            }
        }

        public static void LoadTreasureChest()
        {
            try
            {
                World.Open.Clear();
                var opens = _freeSql.Select<tbl_xwwl_open>().ToList();
                foreach (var open in opens)
                {
                    World.Open.Add(new OpenClass
                    {
                        FLD_PID = open.fld_pid ?? 0,
                        FLD_PIDX = open.fld_pidx ?? 0,
                        FLD_NUMBER = open.fld_number ?? 0,
                        FLD_MAGIC1 = open.fld_magic1 ?? 0,
                        FLD_MAGIC2 = open.fld_magic2 ?? 0,
                        FLD_MAGIC3 = open.fld_magic3 ?? 0,
                        FLD_MAGIC4 = open.fld_magic4 ?? 0,
                        FLD_MAGIC5 = open.fld_magic5 ?? 0,
                        FLD_ThucTinh = open.fld_fj_thuctinh ?? 0,
                        FLD_TienHoa = open.fld_fj_tienhoa ?? 0,
                        FLD_TrungCapPhuHon = open.fld_fj_trungcapphuhon ?? 0,
                        FLD_BD = open.fld_bd ?? 0,
                        FLD_DAYS = open.fld_days ?? 0,
                        CoMoThongBao = open.comothongbao ?? 0,
                        FLD_PP = open.fld_pp ?? 0,
                        FLD_NAME = open.fld_name ?? string.Empty,
                        FLD_NAMEX = open.fld_namex ?? string.Empty,
                        STT_Hop_Event = open.stt_hop_event ?? 0,
                    });
                }
                Console.WriteLine($"✓ Loaded {World.Open.Count} treasure chest templates");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Failed to load treasure chest: {ex.Message}");
            }

        }
        public static void LoadBossDrop()
        {
            try
            {
                World.BossDrop.Clear();
                var drops = _freeSql.Select<tbl_xwwl_bossdrop>().ToList();
                foreach (var drop in drops)
                {
                    World.BossDrop.Add(new DropClass
                    {
                        FLD_LEVEL1 = drop.fld_level1 ?? 0,
                        FLD_LEVEL2 = drop.fld_level2 ?? 0,
                        FLD_PID = drop.fld_pid ?? 0,
                        FLD_PIDNew = drop.fld_pid ?? 0,
                        FLD_PP = drop.fld_pp ?? 0,
                        FLD_NAME = drop.fld_name ?? string.Empty,
                        FLD_MAGIC0 = drop.fld_magic0 ?? 0,
                        FLD_MAGIC1 = drop.fld_magic1 ?? 0,
                        FLD_MAGIC2 = drop.fld_magic2 ?? 0,
                        FLD_MAGIC3 = drop.fld_magic3 ?? 0,
                        FLD_MAGIC4 = drop.fld_magic4 ?? 0,
                        FLD_SoCapPhuHon = drop.fld_socapphuhon ?? 0,
                        FLD_TrungCapPhuHon = drop.fld_trungcapphuhon ?? 0,
                        FLD_TienHoa = drop.fld_tienhoa ?? 0,
                        FLD_KhoaLai = drop.fld_khoalai ?? 0,
                        FLD_MAGICNew0 = drop.fld_magic0 ?? 0,
                        FLD_MAGICNew1 = drop.fld_magic1 ?? 0,
                        FLD_MAGICNew2 = drop.fld_magic2 ?? 0,
                        FLD_MAGICNew3 = drop.fld_magic3 ?? 0,
                        FLD_MAGICNew4 = drop.fld_magic4 ?? 0,
                        CoMoThongBao = drop.comothongbao ?? 0,
                    });
                }
                Console.WriteLine($"✓ Loaded {World.BossDrop.Count} boss drop templates");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Failed to load boss drop: {ex.Message}");
            }
        }
        
        public static void LoadItemOption()
        {
            try
            {
                World.ListItemOption.Clear();
                var itemOptions = _freeSql.Select<tbl_itemoption>().ToList();
                foreach (var itemOption in itemOptions)
                {
                    World.ListItemOption[(long)itemOption.fld_pid] = new ItemOptionClass
                    {
                        ID = itemOption.id ,
                        FLD_PID = (int)itemOption.fld_pid,
                        FLD_NAME = itemOption.fld_name,
                        Bonus_HP = (int)itemOption.bonus_hp,
                        Bonus_PercentHP = (int)itemOption.bonus_percenthp,
                        Bonus_MP = (int)itemOption.bonus_mp,
                        Bonus_PercentMP = (int)itemOption.bonus_percentmp,
                        Bonus_ATK = (int)itemOption.bonus_atk,
                        Bonus_PercentATK = (int)itemOption.bonus_percentatk,
                        Bonus_DF = (int)itemOption.bonus_df,
                        Bonus_PercentDF = (int)itemOption.bonus_percentdf,
                        Bonus_PercentATKSkill = (int)itemOption.bonus_percentatkskill,
                        Bonus_DefSkill = (int)itemOption.bonus_defskill,
                        Bonus_Qigong = (int)itemOption.bonus_qigong,
                        Bonus_DropGold = (int)itemOption.bonus_dropgold,
                        Bonus_Exp = (int)itemOption.bonus_exp,
                        Bonus_Lucky = (int)itemOption.bonus_lucky,
                        Bonus_Accuracy = (int)itemOption.bonus_accuracy,
                        Bonus_Evasion = (int)itemOption.bonus_evasion,
                        Bonus_DiemHoangKim = (int)itemOption.bonus_diemhoangkim,
                        Bonus_ATKMONSTER = (int)itemOption.bonus_atkmonster,
                        Bonus_DEFMONSTER = (int)itemOption.bonus_defmonster,
                    };
                }
                Console.WriteLine($"✓ Loaded {World.ListItemOption.Count} item option templates");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"✗ Failed to load item option: {ex.Message}");
            }
        }

        public static void LoadDCHDrop()
        {
            try
            {
                World.DCH_Drop.Clear();
                var drops = _freeSql.Select<tbl_xwwl_drop_dch>().ToList();
                foreach (var drop in drops)
                {
                    World.DCH_Drop.Add(new DropClass
                    {
                        FLD_LEVEL1 = drop.fld_level1 ?? 0,
                        FLD_LEVEL2 = drop.fld_level2 ?? 0,
                        FLD_PID = drop.fld_pid ?? 0,
                        FLD_PIDNew = drop.fld_pid ?? 0,
                        FLD_PP = drop.fld_pp ?? 0,
                        FLD_NAME = drop.fld_name ?? string.Empty,
                        FLD_MAGIC0 = drop.fld_magic0 ?? 0,
                        FLD_MAGIC1 = drop.fld_magic1 ?? 0,
                        FLD_MAGIC2 = drop.fld_magic2 ?? 0,
                        FLD_MAGIC3 = drop.fld_magic3 ?? 0,
                        FLD_MAGIC4 = drop.fld_magic4 ?? 0,
                        FLD_SoCapPhuHon = drop.fld_socapphuhon ?? 0,
                        FLD_TrungCapPhuHon = drop.fld_trungcapphuhon ?? 0,
                        FLD_TienHoa = drop.fld_tienhoa ?? 0,
                        FLD_KhoaLai = drop.fld_khoalai ?? 0,
                        FLD_MAGICNew0 = drop.fld_magic0 ?? 0,
                        FLD_MAGICNew1 = drop.fld_magic1 ?? 0,
                        FLD_MAGICNew2 = drop.fld_magic2 ?? 0,
                        FLD_MAGICNew3 = drop.fld_magic3 ?? 0,
                        FLD_MAGICNew4 = drop.fld_magic4 ?? 0,
                        CoMoThongBao = drop.comothongbao ?? 0,
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.DCH_Drop.Count} drop templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info($"✗ Failed to load drop: {ex.Message}");
            }
        }

        public static void LoadGSDrop()
        {
            try
            {
                World.Drop_GS.Clear();
                var drops = _freeSql.Select<tbl_xwwl_drop_gs>().ToList();
                foreach (var drop in drops)
                {
                    World.Drop_GS.Add(new DropClass
                    {
                        FLD_LEVEL1 = drop.fld_level1 ?? 0,
                        FLD_LEVEL2 = drop.fld_level2 ?? 0,
                        FLD_PID = drop.fld_pid ?? 0,
                        FLD_PIDNew = drop.fld_pid ?? 0,
                        FLD_PP = drop.fld_pp ?? 0,
                        FLD_NAME = drop.fld_name ?? string.Empty,
                        FLD_MAGIC0 = drop.fld_magic0 ?? 0,
                        FLD_MAGIC1 = drop.fld_magic1 ?? 0,
                        FLD_MAGIC2 = drop.fld_magic2 ?? 0,
                        FLD_MAGIC3 = drop.fld_magic3 ?? 0,
                        FLD_MAGIC4 = drop.fld_magic4 ?? 0,
                        FLD_SoCapPhuHon = drop.fld_socapphuhon ?? 0,
                        FLD_TrungCapPhuHon = drop.fld_trungcapphuhon ?? 0,
                        FLD_TienHoa = drop.fld_tienhoa ?? 0,
                        FLD_KhoaLai = drop.fld_khoalai ?? 0,
                        FLD_MAGICNew0 = drop.fld_magic0 ?? 0,
                        FLD_MAGICNew1 = drop.fld_magic1 ?? 0,
                        FLD_MAGICNew2 = drop.fld_magic2 ?? 0,
                        FLD_MAGICNew3 = drop.fld_magic3 ?? 0,
                        FLD_MAGICNew4 = drop.fld_magic4 ?? 0,
                        CoMoThongBao = drop.comothongbao ?? 0,
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.Drop_GS.Count} drop templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info($"✗ Failed to load drop: {ex.Message}");
            }
        }

        public static void LoadDrop()
        {
            try
            {
                World.Drop.Clear();
                var drops = _freeSql.Select<tbl_xwwl_drop>().ToList();
                foreach (var drop in drops)
                {
                    World.Drop.Add(new DropClass
                    {
                        FLD_LEVEL1 = drop.fld_level1 ?? 0,
                        FLD_LEVEL2 = drop.fld_level2 ?? 0,
                        FLD_PID = drop.fld_pid ?? 0,
                        FLD_PIDNew = drop.fld_pid ?? 0,
                        FLD_PP = drop.fld_pp ?? 0,
                        FLD_NAME = drop.fld_name ?? string.Empty,
                        FLD_MAGIC0 = drop.fld_magic0 ?? 0,
                        FLD_MAGIC1 = drop.fld_magic1 ?? 0,
                        FLD_MAGIC2 = drop.fld_magic2 ?? 0,
                        FLD_MAGIC3 = drop.fld_magic3 ?? 0,
                        FLD_MAGIC4 = drop.fld_magic4 ?? 0,
                        FLD_SoCapPhuHon = drop.fld_socapphuhon ?? 0,
                        FLD_TrungCapPhuHon = drop.fld_trungcapphuhon ?? 0,
                        FLD_TienHoa = drop.fld_tienhoa ?? 0,
                        FLD_KhoaLai = drop.fld_khoalai ?? 0,
                        FLD_MAGICNew0 = drop.fld_magic0 ?? 0,
                        FLD_MAGICNew1 = drop.fld_magic1 ?? 0,
                        FLD_MAGICNew2 = drop.fld_magic2 ?? 0,
                        FLD_MAGICNew3 = drop.fld_magic3 ?? 0,
                        FLD_MAGICNew4 = drop.fld_magic4 ?? 0,
                        CoMoThongBao = drop.comothongbao ?? 0,
                        FLD_DAYS = drop.fld_days ?? 0
                    });
                }
                Logger.Instance.Info($"✓ Loaded {World.Drop.Count} drop templates");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info($"✗ Failed to load drop: {ex.Message}");
            }
        }

        #region Item Templates

        /// <summary>
        /// Get item template by ID
        /// </summary>
        public static tbl_xwwl_item? GetItemTemplate(int itemId)
        {
            lock (_lock)
            {
                return _itemCache.TryGetValue(itemId, out var item) ? item : null;
            }
        }

        /// <summary>
        /// Get all item templates
        /// </summary>
        public static List<tbl_xwwl_item> GetAllItemTemplates()
        {
            lock (_lock)
            {
                return [.. _itemCache.Values];
            }
        }

        /// <summary>
        /// Get items by type
        /// </summary>
        public static List<tbl_xwwl_item> GetItemsByType(int itemType)
        {
            lock (_lock)
            {
                return [.. _itemCache.Values.Where(i => i.fld_type == itemType)];
            }
        }

        /// <summary>
        /// Load item templates into World.ItemList (backward compatibility)
        /// </summary>
        public static void LoadItemTemplatesIntoWorld()
        {
            try
            {
                World.ItemList.Clear();
                
                lock (_lock)
                {
                    foreach (var template in _itemCache.Values)
                    {
                        if (template.fld_pid.HasValue)
                        {
                            var legacyItem = ConvertToLegacyItem(template);
                            World.ItemList[template.fld_pid.Value] = legacyItem;
                        }
                    }
                }

                Logger.Instance.Info($"✓ Loaded {World.ItemList.Count} items into World.ItemList");
            }
            catch (Exception ex)
            {
                Logger.Instance.Info($"✗ Failed to load items into World: {ex.Message}");
            }
        }


        /// <summary>
        /// Convert ItemTemplate to legacy ItmeClass
        /// </summary>
        private static ItmeClass ConvertToLegacyItem(tbl_xwwl_item template)
        {
            return new ItmeClass
            {
                FLD_PID = template.fld_pid ?? 0,
                ItmeNAME = template.fld_name ?? string.Empty,
                FLD_TYPE = template.fld_type ?? 0,
                FLD_WEIGHT = template.fld_weight ?? 0,
                FLD_LEVEL = template.fld_level ?? 0,
                FLD_UP_LEVEL = template.fld_up_level ?? 0,
                FLD_RECYCLE_MONEY = template.fld_recycle_money ?? 0,
                FLD_SALE_MONEY = template.fld_sale_money ?? 0,
                FLD_QUESTITEM = template.fld_questitem ?? 0,
                FLD_NJ = template.fld_nj ?? 0,
                FLD_DF = template.fld_df ?? 0,
                FLD_AT = template.fld_at1 ?? 0,
                FLD_AT_Max = template.fld_at2 ?? 0,
                FLD_JOB_LEVEL = template.fld_job_level ?? 0,
                FLD_ZX = template.fld_zx ?? 0,
                FLD_NEED_MONEY = template.fld_need_money ?? 0,
                FLD_NEED_FIGHTEXP = template.fld_need_fightexp ?? 0,
                FLD_MAGIC0 = template.fld_magic1 ?? 0,
                FLD_MAGIC1 = template.fld_magic2 ?? 0,
                FLD_MAGIC2 = template.fld_magic3 ?? 0,
                FLD_MAGIC3 = template.fld_magic4 ?? 0,
                FLD_MAGIC4 = template.fld_magic5 ?? 0,
                FLD_SIDE = template.fld_side ?? 0,
                FLD_LOCK = template.fld_lock ?? 0,
                FLD_SERIES = template.fld_series ?? 0,
                FLD_INTEGRATION = template.fld_integration ?? 0,
                FLD_RESIDE1 = template.fld_reside1 ?? 0,
                FLD_RESIDE2 = template.fld_reside2 ?? 0,
                FLD_SEX = template.fld_sex ?? 0,
                FLD_XW = template.fld_wx ?? 0,
                FLD_XWJD = template.fld_wxjd ?? 0
            };
        }

        #endregion

        #region Monster Templates

        /// <summary>
        /// Get monster template by ID
        /// </summary>
        public static tbl_xwwl_monster? GetMonsterTemplate(int monsterId)
        {
            lock (_lock)
            {
                return _monsterCache.TryGetValue(monsterId, out var monster) ? monster : null;
            }
        }

        /// <summary>
        /// Get all monster templates
        /// </summary>
        public static List<tbl_xwwl_monster> GetAllMonsterTemplates()
        {
            lock (_lock)
            {
                return [.. _monsterCache.Values];
            }
        }

        /// <summary>
        /// Get boss monsters
        /// </summary>
        public static List<tbl_xwwl_monster> GetBossMonsters()
        {
            lock (_lock)
            {
                return _monsterCache.Values.Where(m => m.fld_boss == 1).ToList();
            }
        }

        public static bool InsertMonsterSetBase(tbl_xwwl_monster_set_base monsterSetBase)
        {
            try
            {
                // Đảm bảo fld_index không được set để database tự động tạo
                monsterSetBase.fld_index = 0;

                _freeSql.Insert(monsterSetBase)
                    .ExecuteAffrows();
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to insert monster set base: {ex.Message}");
                Logger.Instance.Error($"✗ Stack trace: {ex.StackTrace}");
                return false;
            }
        }

        public static void LoadMonsterSetBase()
        {
            try
            {
                World.MapList.Clear();
                World.NpcList.Clear();
                World.SpotMap.Clear();
                World.PortalCountPerMap.Clear();

                var monsterSetBase = _freeSql.Select<tbl_xwwl_monster_set_base>().ToList();
                foreach (var monster in monsterSetBase)
                {
                    var pid = (int)monster.fld_pid;
                    var amount = (int)monster.fld_amount;
                    var aoe = (int)monster.fld_aoe;
                    var level = (int)monster.fld_level;
                    var map = (int)monster.fld_mid;
                    float baseX = (float)monster.fld_x;
                    float baseY = (float)monster.fld_y;
                    if (monster.fld_active != 1)
                    {
                        continue;
                    }

                    if (pid < 10000)
                    {
                        World.LoadANpc(pid, monster.fld_name, map, (float)monster.fld_face0, (float)monster.fld_face, baseX, baseY, 15);
                    }
                    else
                    {
                        for (int i = 0; i < amount; i++)
                        {
                            float offsetX = RNG.Next(-aoe, aoe);
                            float offsetY = RNG.Next(-aoe, aoe);
                            float x = baseX + offsetX;
                            float y = baseY + offsetY;
                            float face0 = (float)monster.fld_face0 + RNG.Next(-100,100)/100;
                            float face = (float)monster.fld_face + RNG.Next(-100,100)/100;
                            World.LoadAMonster(
                                monster.fld_index ,
                                pid,
                                monster.fld_name,
                                level,
                                (int)monster.fld_exp,
                                (int)monster.fld_gold,
                                (int)monster.fld_npc,
                                (int)monster.fld_hp,
                                (int)monster.fld_at,
                                (int)monster.fld_df,
                                (int)monster.fld_accuracy,
                                (int)monster.fld_evasion,
                                (int)monster.fld_auto,
                                (int)monster.fld_boss,
                                (int)monster.fld_qitemdrop,
                                (int)monster.fld_qdroppp,
                                (int)monster.fld_newtime,
                                map,
                                (float)face0,
                                (float)face,
                                x,
                                y,
                                15,
                                (int)monster.fld_freedrop
                            );
                        }
                        float density = amount / (aoe * aoe);
                        if (aoe > 0 && amount > 1 && density >= 0.1)
                        {
                            if (!World.PortalCountPerMap.TryGetValue(level, out var currentCount))
                            {
                                currentCount = 0;
                            }

                            World.PortalCountPerMap[level] = currentCount + 1;

                            X_Toa_Do_Class coord = new X_Toa_Do_Class
                            {
                                Rxjh_Map = map,
                                Rxjh_X = baseX,
                                Rxjh_Y = baseY,
                                Rxjh_name = map + "" + (currentCount + 1)
                            };
                            World.AddPortalToMap(level, currentCount + 1, coord);
                        }
                    }
                }

                Logger.Instance.Info($"✓ Loaded {World.NpcList.Count} monster set base");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to load monster set base: {ex.Message}");
            }
        }

        /// <summary>
        /// Load monster templates into World.MonsterTemplateList (backward compatibility)
        /// </summary>
        public static void LoadMonsterTemplatesIntoWorld()
        {
            try
            {
                World.MonsterTemplateList.Clear();

                lock (_lock)
                {
                    foreach (var template in _monsterCache.Values)
                    {
                        if (template.fld_pid.HasValue)
                        {
                            var legacyMonster = ConvertToLegacyMonster(template);
                            World.MonsterTemplateList[template.fld_pid.Value] = legacyMonster;
                        }
                    }
                }

                Logger.Instance.Info($"✓ Loaded {World.MonsterTemplateList.Count} monsters into World.MonsterTemplateList");
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to load monsters into World: {ex.Message}");
            }
        }

        /// <summary>
        /// Convert MonsterTemplate to legacy MonSterClss
        /// </summary>
        private static MonSterClss ConvertToLegacyMonster(tbl_xwwl_monster template)
        {
            return new MonSterClss
            {
                fld_pid = template.fld_pid ?? 0,
                fld_at = template.fld_at ?? 0,
                fld_auto = template.fld_auto ?? 0,
                fld_boss = template.fld_boss ?? 0,
                fld_df = template.fld_df ?? 0,
                fld_level = template.fld_level ?? 0,
                fld_name = template.fld_name ?? string.Empty,
                fld_exp = template.fld_exp ?? 0,
                fld_hp = template.fld_hp ?? 0,
                fld_npc = template.fld_npc ?? 0,
                fld_quest = template.fld_quest ?? 0,
                fld_questid = template.fld_questid ?? 0,
                fld_stages = template.fld_stages ?? 0,
                fld_questitem = template.fld_questitem ?? 0,
                fld_pp = template.fld_pp ?? 0
            };
        }

        #endregion

        public static bool DeleteGiftCode(string code)
        {
            try
            {
                return _freeSql.Delete<giftcode>()
                    .Where(a => a.code == code)
                    .ExecuteAffrows() > 0;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to delete gift code: {ex.Message}");
                return false;
            }
        }

        #region Cache Management

        /// <summary>
        /// Refresh all templates from database
        /// </summary>
        public static async Task<bool> RefreshAsync()
        {
            try
            {
                await LoadAllTemplatesAsync();
                Logger.Instance.Info("✓ Templates refreshed successfully");
                return true;
            }
            catch (Exception ex)
            {
                Logger.Instance.Error($"✗ Failed to refresh templates: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Get cache statistics
        /// </summary>
        public static (int ItemCount, int MonsterCount) GetCacheStats()
        {
            lock (_lock)
            {
                return (_itemCache.Count, _monsterCache.Count);
            }
        }

        #endregion

        /// <summary>
        /// Check if PublicDb is ready
        /// </summary>
        public static bool IsReady => _isInitialized;
    }
}
