-- Complete script to add auto-increment primary keys to all tables
-- Script hoàn chỉnh để thêm auto-increment primary key cho tất cả các bảng
-- Generated on 2025-07-11
-- Đ<PERSON><PERSON>c tạo vào 2025-07-11

-- ========================================
-- TABLES NEEDING AUTO-INCREMENT SETUP
-- CÁC BẢNG CẦN THIẾT LẬP AUTO-INCREMENT
-- ========================================

-- Based on analysis of FreeSql entity classes, these tables need auto-increment setup:
-- Dựa trên phân tích các entity classes FreeSql, các bảng này cần thiết lập auto-increment:

-- ========================================
-- Account Database Tables
-- ========================================

-- Table: account
CREATE SEQUENCE IF NOT EXISTS "account_id_seq";
SELECT setval('account_id_seq', COALESCE((SELECT MAX("id") FROM "account"), 0) + 1, false);
ALTER TABLE "account"
   ALTER COLUMN "id" SET DEFAULT nextval('account_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "account_id_seq" OWNED BY "account"."id";
SELECT 'Setup completed for account' as status;

-- Table: addcash
CREATE SEQUENCE IF NOT EXISTS "addcash_id_seq";
SELECT setval('addcash_id_seq', COALESCE((SELECT MAX("id") FROM "addcash"), 0) + 1, false);
ALTER TABLE "addcash"
   ALTER COLUMN "id" SET DEFAULT nextval('addcash_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "addcash_id_seq" OWNED BY "addcash"."id";
SELECT 'Setup completed for addcash' as status;

-- Table: banned
CREATE SEQUENCE IF NOT EXISTS "banned_id_seq";
SELECT setval('banned_id_seq', COALESCE((SELECT MAX("id") FROM "banned"), 0) + 1, false);
ALTER TABLE "banned"
   ALTER COLUMN "id" SET DEFAULT nextval('banned_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "banned_id_seq" OWNED BY "banned"."id";
SELECT 'Setup completed for banned' as status;

-- Table: doanhthu
CREATE SEQUENCE IF NOT EXISTS "doanhthu_id_seq";
SELECT setval('doanhthu_id_seq', COALESCE((SELECT MAX("id") FROM "doanhthu"), 0) + 1, false);
ALTER TABLE "doanhthu"
   ALTER COLUMN "id" SET DEFAULT nextval('doanhthu_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "doanhthu_id_seq" OWNED BY "doanhthu"."id";
SELECT 'Setup completed for doanhthu' as status;

-- Table: ipcheck
CREATE SEQUENCE IF NOT EXISTS "ipcheck_id_seq";
SELECT setval('ipcheck_id_seq', COALESCE((SELECT MAX("id") FROM "ipcheck"), 0) + 1, false);
ALTER TABLE "ipcheck"
   ALTER COLUMN "id" SET DEFAULT nextval('ipcheck_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "ipcheck_id_seq" OWNED BY "ipcheck"."id";
SELECT 'Setup completed for ipcheck' as status;

-- Table: tbl_more_run
CREATE SEQUENCE IF NOT EXISTS "tbl_more_run_id_seq";
SELECT setval('tbl_more_run_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_more_run"), 0) + 1, false);
ALTER TABLE "tbl_more_run"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_more_run_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_more_run_id_seq" OWNED BY "tbl_more_run"."id";
SELECT 'Setup completed for tbl_more_run' as status;

-- Table: tbl_online
CREATE SEQUENCE IF NOT EXISTS "tbl_online_id_seq";
SELECT setval('tbl_online_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_online"), 0) + 1, false);
ALTER TABLE "tbl_online"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_online_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_online_id_seq" OWNED BY "tbl_online"."id";
SELECT 'Setup completed for tbl_online' as status;

-- Table: tbl_trucash
CREATE SEQUENCE IF NOT EXISTS "tbl_trucash_id_seq";
SELECT setval('tbl_trucash_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_trucash"), 0) + 1, false);
ALTER TABLE "tbl_trucash"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_trucash_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_trucash_id_seq" OWNED BY "tbl_trucash"."id";
SELECT 'Setup completed for tbl_trucash' as status;

-- Table: tbl_updatelog
CREATE SEQUENCE IF NOT EXISTS "tbl_updatelog_id_seq";
SELECT setval('tbl_updatelog_id_seq', COALESCE((SELECT MAX("id") FROM "tbl_updatelog"), 0) + 1, false);
ALTER TABLE "tbl_updatelog"
   ALTER COLUMN "id" SET DEFAULT nextval('tbl_updatelog_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "tbl_updatelog_id_seq" OWNED BY "tbl_updatelog"."id";
SELECT 'Setup completed for tbl_updatelog' as status;

-- ========================================
-- BBG Database Tables
-- ========================================

-- Table: cash_shop_log
CREATE SEQUENCE IF NOT EXISTS "cash_shop_log_id_seq";
SELECT setval('cash_shop_log_id_seq', COALESCE((SELECT MAX("id") FROM "cash_shop_log"), 0) + 1, false);
ALTER TABLE "cash_shop_log"
   ALTER COLUMN "id" SET DEFAULT nextval('cash_shop_log_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "cash_shop_log_id_seq" OWNED BY "cash_shop_log"."id";
SELECT 'Setup completed for cash_shop_log' as status;

-- Table: cashshop
CREATE SEQUENCE IF NOT EXISTS "cashshop_id_seq";
SELECT setval('cashshop_id_seq', COALESCE((SELECT MAX("id") FROM "cashshop"), 0) + 1, false);
ALTER TABLE "cashshop"
   ALTER COLUMN "id" SET DEFAULT nextval('cashshop_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "cashshop_id_seq" OWNED BY "cashshop"."id";
SELECT 'Setup completed for cashshop' as status;

-- Table: giftcodelog
CREATE SEQUENCE IF NOT EXISTS "giftcodelog_id_seq";
SELECT setval('giftcodelog_id_seq', COALESCE((SELECT MAX("id") FROM "giftcodelog"), 0) + 1, false);
ALTER TABLE "giftcodelog"
   ALTER COLUMN "id" SET DEFAULT nextval('giftcodelog_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "giftcodelog_id_seq" OWNED BY "giftcodelog"."id";
SELECT 'Setup completed for giftcodelog' as status;

-- Table: item_vong_quay
CREATE SEQUENCE IF NOT EXISTS "item_vong_quay_id_seq";
SELECT setval('item_vong_quay_id_seq', COALESCE((SELECT MAX("id") FROM "item_vong_quay"), 0) + 1, false);
ALTER TABLE "item_vong_quay"
   ALTER COLUMN "id" SET DEFAULT nextval('item_vong_quay_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "item_vong_quay_id_seq" OWNED BY "item_vong_quay"."id";
SELECT 'Setup completed for item_vong_quay' as status;

-- Table: itmeclss
CREATE SEQUENCE IF NOT EXISTS "itmeclss_id_seq";
SELECT setval('itmeclss_id_seq', COALESCE((SELECT MAX("id") FROM "itmeclss"), 0) + 1, false);
ALTER TABLE "itmeclss"
   ALTER COLUMN "id" SET DEFAULT nextval('itmeclss_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "itmeclss_id_seq" OWNED BY "itmeclss"."id";
SELECT 'Setup completed for itmeclss' as status;

-- Table: mailcod
CREATE SEQUENCE IF NOT EXISTS "mailcod_id_seq";
SELECT setval('mailcod_id_seq', COALESCE((SELECT MAX("id") FROM "mailcod"), 0) + 1, false);
ALTER TABLE "mailcod"
   ALTER COLUMN "id" SET DEFAULT nextval('mailcod_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "mailcod_id_seq" OWNED BY "mailcod"."id";
SELECT 'Setup completed for mailcod' as status;

-- Table: marketplace
CREATE SEQUENCE IF NOT EXISTS "marketplace_id_seq";
SELECT setval('marketplace_id_seq', COALESCE((SELECT MAX("id") FROM "marketplace"), 0) + 1, false);
ALTER TABLE "marketplace"
   ALTER COLUMN "id" SET DEFAULT nextval('marketplace_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "marketplace_id_seq" OWNED BY "marketplace"."id";
SELECT 'Setup completed for marketplace' as status;

-- Table: order_detail
CREATE SEQUENCE IF NOT EXISTS "order_detail_id_seq";
SELECT setval('order_detail_id_seq', COALESCE((SELECT MAX("id") FROM "order_detail"), 0) + 1, false);
ALTER TABLE "order_detail"
   ALTER COLUMN "id" SET DEFAULT nextval('order_detail_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "order_detail_id_seq" OWNED BY "order_detail"."id";
SELECT 'Setup completed for order_detail' as status;

-- Table: shopcategory
CREATE SEQUENCE IF NOT EXISTS "shopcategory_id_seq";
SELECT setval('shopcategory_id_seq', COALESCE((SELECT MAX("id") FROM "shopcategory"), 0) + 1, false);
ALTER TABLE "shopcategory"
   ALTER COLUMN "id" SET DEFAULT nextval('shopcategory_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "shopcategory_id_seq" OWNED BY "shopcategory"."id";
SELECT 'Setup completed for shopcategory' as status;

-- Table: vong_quay_result (Note: this has long id, not int)
-- CREATE SEQUENCE IF NOT EXISTS "vong_quay_result_id_seq";
-- SELECT setval('vong_quay_result_id_seq', COALESCE((SELECT MAX("id") FROM "vong_quay_result"), 0) + 1, false);
-- ALTER TABLE "vong_quay_result"
--    ALTER COLUMN "id" SET DEFAULT nextval('vong_quay_result_id_seq'),
--    ALTER COLUMN "id" SET NOT NULL;
-- ALTER SEQUENCE "vong_quay_result_id_seq" OWNED BY "vong_quay_result"."id";
-- SELECT 'Setup completed for vong_quay_result' as status;

-- ========================================
-- VERIFICATION QUERIES
-- CÁC TRUY VẤN XÁC MINH
-- ========================================

-- Check all sequences created
SELECT schemaname, sequencename, last_value, start_value, increment_by
FROM pg_sequences 
WHERE sequencename LIKE '%_id_seq'
ORDER BY sequencename;

-- Check column defaults for Account tables
SELECT 
    table_name,
    column_name,
    column_default,
    is_nullable
FROM information_schema.columns 
WHERE table_name IN (
    'account', 'addcash', 'banned', 'doanhthu', 'ipcheck', 
    'tbl_more_run', 'tbl_online', 'tbl_trucash', 'tbl_updatelog'
) AND column_name = 'id'
ORDER BY table_name;

-- Check column defaults for BBG tables
SELECT
    table_name,
    column_name,
    column_default,
    is_nullable
FROM information_schema.columns
WHERE table_name IN (
    'cash_shop_log', 'cashshop', 'giftcodelog', 'item_vong_quay',
    'itmeclss', 'mailcod', 'marketplace', 'order_detail', 'shopcategory'
) AND column_name = 'id'
ORDER BY table_name;

-- ========================================
-- Game Database Tables
-- ========================================

-- Table: bachbaocacrecord
CREATE SEQUENCE IF NOT EXISTS "bachbaocacrecord_id_seq";
SELECT setval('bachbaocacrecord_id_seq', COALESCE((SELECT MAX("id") FROM "bachbaocacrecord"), 0) + 1, false);
ALTER TABLE "bachbaocacrecord"
   ALTER COLUMN "id" SET DEFAULT nextval('bachbaocacrecord_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "bachbaocacrecord_id_seq" OWNED BY "bachbaocacrecord"."id";
SELECT 'Setup completed for bachbaocacrecord' as status;

-- Table: bangchien_tiendatcuoc
CREATE SEQUENCE IF NOT EXISTS "bangchien_tiendatcuoc_id_seq";
SELECT setval('bangchien_tiendatcuoc_id_seq', COALESCE((SELECT MAX("id") FROM "bangchien_tiendatcuoc"), 0) + 1, false);
ALTER TABLE "bangchien_tiendatcuoc"
   ALTER COLUMN "id" SET DEFAULT nextval('bangchien_tiendatcuoc_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "bangchien_tiendatcuoc_id_seq" OWNED BY "bangchien_tiendatcuoc"."id";
SELECT 'Setup completed for bangchien_tiendatcuoc' as status;

-- Table: congthanhchien_thanhchu
CREATE SEQUENCE IF NOT EXISTS "congthanhchien_thanhchu_id_seq";
SELECT setval('congthanhchien_thanhchu_id_seq', COALESCE((SELECT MAX("id") FROM "congthanhchien_thanhchu"), 0) + 1, false);
ALTER TABLE "congthanhchien_thanhchu"
   ALTER COLUMN "id" SET DEFAULT nextval('congthanhchien_thanhchu_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "congthanhchien_thanhchu_id_seq" OWNED BY "congthanhchien_thanhchu"."id";
SELECT 'Setup completed for congthanhchien_thanhchu' as status;

-- Table: drugrecord
CREATE SEQUENCE IF NOT EXISTS "drugrecord_id_seq";
SELECT setval('drugrecord_id_seq', COALESCE((SELECT MAX("id") FROM "drugrecord"), 0) + 1, false);
ALTER TABLE "drugrecord"
   ALTER COLUMN "id" SET DEFAULT nextval('drugrecord_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "drugrecord_id_seq" OWNED BY "drugrecord"."id";
SELECT 'Setup completed for drugrecord' as status;

-- Table: eventtop
CREATE SEQUENCE IF NOT EXISTS "eventtop_id_seq";
SELECT setval('eventtop_id_seq', COALESCE((SELECT MAX("id") FROM "eventtop"), 0) + 1, false);
ALTER TABLE "eventtop"
   ALTER COLUMN "id" SET DEFAULT nextval('eventtop_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "eventtop_id_seq" OWNED BY "eventtop"."id";
SELECT 'Setup completed for eventtop' as status;

-- Table: eventtop_dch
CREATE SEQUENCE IF NOT EXISTS "eventtop_dch_id_seq";
SELECT setval('eventtop_dch_id_seq', COALESCE((SELECT MAX("id") FROM "eventtop_dch"), 0) + 1, false);
ALTER TABLE "eventtop_dch"
   ALTER COLUMN "id" SET DEFAULT nextval('eventtop_dch_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "eventtop_dch_id_seq" OWNED BY "eventtop_dch"."id";
SELECT 'Setup completed for eventtop_dch' as status;

-- Table: exchangecharacter
CREATE SEQUENCE IF NOT EXISTS "exchangecharacter_id_seq";
SELECT setval('exchangecharacter_id_seq', COALESCE((SELECT MAX("id") FROM "exchangecharacter"), 0) + 1, false);
ALTER TABLE "exchangecharacter"
   ALTER COLUMN "id" SET DEFAULT nextval('exchangecharacter_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "exchangecharacter_id_seq" OWNED BY "exchangecharacter"."id";
SELECT 'Setup completed for exchangecharacter' as status;

-- Table: giftcode
CREATE SEQUENCE IF NOT EXISTS "giftcode_id_seq";
SELECT setval('giftcode_id_seq', COALESCE((SELECT MAX("id") FROM "giftcode"), 0) + 1, false);
ALTER TABLE "giftcode"
   ALTER COLUMN "id" SET DEFAULT nextval('giftcode_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "giftcode_id_seq" OWNED BY "giftcode"."id";
SELECT 'Setup completed for giftcode' as status;

-- Table: itemrecord
CREATE SEQUENCE IF NOT EXISTS "itemrecord_id_seq";
SELECT setval('itemrecord_id_seq', COALESCE((SELECT MAX("id") FROM "itemrecord"), 0) + 1, false);
ALTER TABLE "itemrecord"
   ALTER COLUMN "id" SET DEFAULT nextval('itemrecord_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "itemrecord_id_seq" OWNED BY "itemrecord"."id";
SELECT 'Setup completed for itemrecord' as status;

-- Table: log_deleteitem
CREATE SEQUENCE IF NOT EXISTS "log_deleteitem_id_seq";
SELECT setval('log_deleteitem_id_seq', COALESCE((SELECT MAX("id") FROM "log_deleteitem"), 0) + 1, false);
ALTER TABLE "log_deleteitem"
   ALTER COLUMN "id" SET DEFAULT nextval('log_deleteitem_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "log_deleteitem_id_seq" OWNED BY "log_deleteitem"."id";
SELECT 'Setup completed for log_deleteitem' as status;

-- Table: log_thelucchien
CREATE SEQUENCE IF NOT EXISTS "log_thelucchien_id_seq";
SELECT setval('log_thelucchien_id_seq', COALESCE((SELECT MAX("id") FROM "log_thelucchien"), 0) + 1, false);
ALTER TABLE "log_thelucchien"
   ALTER COLUMN "id" SET DEFAULT nextval('log_thelucchien_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "log_thelucchien_id_seq" OWNED BY "log_thelucchien"."id";
SELECT 'Setup completed for log_thelucchien' as status;

-- Table: loginrecord
CREATE SEQUENCE IF NOT EXISTS "loginrecord_id_seq";
SELECT setval('loginrecord_id_seq', COALESCE((SELECT MAX("id") FROM "loginrecord"), 0) + 1, false);
ALTER TABLE "loginrecord"
   ALTER COLUMN "id" SET DEFAULT nextval('loginrecord_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "loginrecord_id_seq" OWNED BY "loginrecord"."id";
SELECT 'Setup completed for loginrecord' as status;

-- Table: loginrecord_mac
CREATE SEQUENCE IF NOT EXISTS "loginrecord_mac_id_seq";
SELECT setval('loginrecord_mac_id_seq', COALESCE((SELECT MAX("id") FROM "loginrecord_mac"), 0) + 1, false);
ALTER TABLE "loginrecord_mac"
   ALTER COLUMN "id" SET DEFAULT nextval('loginrecord_mac_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "loginrecord_mac_id_seq" OWNED BY "loginrecord_mac"."id";
SELECT 'Setup completed for loginrecord_mac' as status;

-- Table: logpk
CREATE SEQUENCE IF NOT EXISTS "logpk_id_seq";
SELECT setval('logpk_id_seq', COALESCE((SELECT MAX("id") FROM "logpk"), 0) + 1, false);
ALTER TABLE "logpk"
   ALTER COLUMN "id" SET DEFAULT nextval('logpk_id_seq'),
   ALTER COLUMN "id" SET NOT NULL;
ALTER SEQUENCE "logpk_id_seq" OWNED BY "logpk"."id";
SELECT 'Setup completed for logpk' as status;
