using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace YulgangShared.Configuration;

public class ConfigurationService : IHostedService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;
    private readonly List<FileSystemWatcher> _watchers = new();
    private readonly Dictionary<string, object> _configCache = new();

    public event Action<string, object>? ConfigChanged;

    public ConfigurationService(IConfiguration configuration, ILogger<ConfigurationService> logger)
    {
        _configuration = configuration;
        _logger = logger;
    }

    public async Task StartAsync(CancellationToken cancellationToken)
    {
        // Watch config files for changes
        var configPaths = _configuration.GetSection("ConfigPaths").Get<string[]>() ?? Array.Empty<string>();
        
        foreach (var configPath in configPaths)
        {
            if (File.Exists(configPath))
            {
                WatchConfigFile(configPath);
                await LoadConfigFile(configPath);
            }
        }

        _logger.LogInformation("Configuration service started, watching {Count} config files", configPaths.Length);
    }

    public Task StopAsync(CancellationToken cancellationToken)
    {
        foreach (var watcher in _watchers)
        {
            watcher.Dispose();
        }
        _watchers.Clear();
        return Task.CompletedTask;
    }

    private void WatchConfigFile(string configPath)
    {
        var directory = Path.GetDirectoryName(configPath)!;
        var fileName = Path.GetFileName(configPath);

        var watcher = new FileSystemWatcher(directory, fileName)
        {
            NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
            EnableRaisingEvents = true
        };

        watcher.Changed += async (sender, e) =>
        {
            try
            {
                // Debounce multiple file system events
                await Task.Delay(500);
                await LoadConfigFile(e.FullPath);
                _logger.LogInformation("Config file reloaded: {Path}", e.FullPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reloading config file: {Path}", e.FullPath);
            }
        };

        _watchers.Add(watcher);
    }

    private async Task LoadConfigFile(string configPath)
    {
        try
        {
            var content = await File.ReadAllTextAsync(configPath);
            var configName = Path.GetFileNameWithoutExtension(configPath);
            
            // Parse JSON config
            var configObject = JsonSerializer.Deserialize<Dictionary<string, object>>(content);
            
            if (configObject != null)
            {
                _configCache[configName] = configObject;
                ConfigChanged?.Invoke(configName, configObject);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load config file: {Path}", configPath);
        }
    }

    public T? GetConfig<T>(string configName) where T : class
    {
        if (_configCache.TryGetValue(configName, out var config))
        {
            var json = JsonSerializer.Serialize(config);
            return JsonSerializer.Deserialize<T>(json);
        }
        return null;
    }

    public bool HasConfig(string configName)
    {
        return _configCache.ContainsKey(configName);
    }
}

// Extension methods for DI
public static class ConfigurationServiceExtensions
{
    public static IServiceCollection AddYulgangConfiguration(this IServiceCollection services)
    {
        services.AddSingleton<ConfigurationService>();
        services.AddHostedService<ConfigurationService>(provider => provider.GetRequiredService<ConfigurationService>());
        return services;
    }
}
